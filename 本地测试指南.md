# 医院科室绩效计算器 - 本地测试指南

## 🎯 概述

本文档详细说明如何在本地环境测试Web版本的医院科室绩效计算器，确保所有功能正常后再部署到腾讯云。

## 📋 环境要求

- **Python**: 3.7+
- **操作系统**: Windows/macOS/Linux
- **内存**: 至少4GB
- **硬盘空间**: 至少2GB可用空间

## 🚀 快速开始

### 第一步：安装依赖

```bash
# 进入项目目录
cd /Users/<USER>/PythonProjects/科室绩效计算器

# 创建Python虚拟环境
python3 -m venv web_venv

# 激活虚拟环境
# macOS/Linux:
source web_venv/bin/activate
# Windows:
# web_venv\Scripts\activate

# 安装Web应用依赖
pip install -r web_requirements.txt
```

### 第二步：配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
# macOS/Linux:
nano .env
# Windows:
# notepad .env
```

修改`.env`文件内容（本地测试配置）：
```bash
# Flask应用配置
FLASK_ENV=development
SECRET_KEY=local-development-secret-key

# 本地SQLite数据库（简单测试）
# DATABASE_URL会被app.py中的开发配置覆盖

# Redis配置（如果安装了Redis）
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=52428800

# 其他配置可以暂时留空或使用默认值
```

### 第三步：初始化数据库

```bash
# 确保虚拟环境已激活
source web_venv/bin/activate  # macOS/Linux
# web_venv\Scripts\activate   # Windows

# 创建数据库表
python -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('数据库表创建成功！')
"
```

### 第四步：启动应用

```bash
# 启动Flask开发服务器
python app.py
```

应该看到类似输出：
```
 * Running on http://127.0.0.1:5000
 * Debug mode: on
 * Restarting with stat
 * Debugger is active!
```

### 第五步：测试访问

打开浏览器访问：`http://localhost:5000`

## 🧪 功能测试清单

### 1. 基础功能测试

#### 1.1 页面访问测试
- [ ] 主页正常显示：`http://localhost:5000`
- [ ] 登录页面：`http://localhost:5000/login`
- [ ] 注册页面：`http://localhost:5000/register`

#### 1.2 用户注册测试
```
测试步骤：
1. 访问注册页面
2. 填写用户信息：
   - 用户名：test_user
   - 邮箱：<EMAIL>
   - 医生姓名：测试医生
   - 密码：123456
3. 点击注册
4. 检查是否跳转到登录页面并显示成功消息
```

#### 1.3 用户登录测试
```
测试步骤：
1. 使用注册的账号登录
2. 检查是否跳转到工作台
3. 检查用户信息是否正确显示
```

### 2. 核心功能测试

#### 2.1 准备测试数据

首先创建测试数据文件：

```bash
# 创建测试数据目录
mkdir -p test_data

# 创建patients.xlsx测试文件
cat > create_test_data.py << 'EOF'
import pandas as pd
import os

# 创建测试目录
os.makedirs('test_data', exist_ok=True)

# 创建测试患者数据
patients_data = {
    '患者姓名': ['张三', '李四', '王五', '赵六', '陈七'],
    '住院医生': ['赖红琳', '吴西雅', '童波', '夏顺生', '邹国明'],
    '科室': ['呼吸内科'] * 5,
    '床号': ['101', '102', '103', '104', '105'],
    '住院号': ['2025001', '2025002', '2025003', '2025004', '2025005']
}

df_patients = pd.DataFrame(patients_data)
df_patients.to_excel('test_data/patients.xlsx', index=False)

# 创建绩效数据CSV文件
def create_performance_csv(filename, patients):
    performance_data = {
        'xm': patients,
        'xz': [10.5, 15.0, 12.5, 8.0, 20.0],  # 协助点数
        'zx': [25.0, 30.0, 22.5, 18.0, 35.0], # 执行点数
        'name': ['胸部CT', '心电图', '血常规', '尿常规', '肝功能'],
    }
    df = pd.DataFrame(performance_data)
    df.to_csv(f'test_data/{filename}', index=False, encoding='utf-8')

# 创建各个病区的测试数据
create_performance_csv('1.csv', ['张三', '李四'])  # 一病区
create_performance_csv('2.csv', ['王五', '赵六'])  # 二病区  
create_performance_csv('3.csv', ['陈七'])         # 三病区
create_performance_csv('icu.csv', ['张三', '王五']) # 呼吸ICU

print("测试数据创建完成！")
print("文件列表：")
for file in os.listdir('test_data'):
    print(f"  - test_data/{file}")
EOF

# 运行脚本创建测试数据
python create_test_data.py
```

#### 2.2 文件上传和计算测试

```
测试步骤：
1. 登录后访问"绩效计算"页面
2. 上传准备好的5个测试文件：
   - patients.xlsx
   - 1.csv
   - 2.csv  
   - 3.csv
   - icu.csv
3. 设置任务名称：本地测试任务
4. 选择重复数据处理策略
5. 点击"开始计算"
6. 检查是否能正常处理并生成报告
```

#### 2.3 医生管理测试

```
测试步骤：
1. 访问"医生管理"页面
2. 查看当前医生分组
3. 测试添加新医生
4. 测试移动医生到其他分组
5. 测试删除医生
```

### 3. 错误处理测试

#### 3.1 文件格式错误测试
```
测试步骤：
1. 上传错误格式的文件（如txt文件）
2. 检查是否有适当的错误提示
3. 上传缺少必需列的Excel文件
4. 检查错误处理机制
```

#### 3.2 权限测试
```
测试步骤：
1. 未登录状态下访问受保护页面
2. 检查是否正确重定向到登录页面
3. 测试用户只能访问自己的数据
```

## 🛠️ 常见问题和解决方案

### Q1: 虚拟环境激活失败

**macOS/Linux:**
```bash
# 确保使用正确的Python版本
python3 -m venv web_venv
source web_venv/bin/activate
```

**Windows:**
```cmd
python -m venv web_venv
web_venv\Scripts\activate
```

### Q2: 依赖安装失败

```bash
# 升级pip
pip install --upgrade pip

# 如果某些包安装失败，尝试：
pip install --upgrade setuptools wheel

# 逐个安装关键依赖
pip install Flask==2.3.2
pip install Flask-SQLAlchemy==3.0.5
pip install Flask-Login==0.6.2
```

### Q3: 数据库初始化失败

```bash
# 删除现有数据库文件重新创建
rm performance_dev.db

# 重新初始化
python -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('数据库重新创建成功！')
"
```

### Q4: 端口占用问题

```bash
# 查看端口占用
# macOS/Linux:
lsof -i :5000
# Windows:
netstat -ano | findstr :5000

# 杀死占用进程或换端口
# 在app.py最后一行修改：
app.run(debug=True, host='0.0.0.0', port=5001)
```

### Q5: 文件上传权限问题

```bash
# 确保uploads目录有写权限
mkdir -p uploads output logs
chmod 755 uploads output logs

# Windows下可能需要在文件夹属性中设置权限
```

## 🔧 调试技巧

### 1. 启用详细日志

修改`app.py`中的日志级别：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. 查看数据库内容

```bash
# 安装SQLite浏览器工具
pip install sqlite-web

# 启动Web界面查看数据库
sqlite_web performance_dev.db
```

### 3. 测试特定功能

```bash
# 单独测试患者数据处理
python -c "
from src.core.patient_processor import PatientProcessor
processor = PatientProcessor()
data = processor.load_patients('test_data/patients.xlsx')
print('患者数据加载成功:', len(data), '条记录')
"

# 单独测试医生管理
python -c "
from src.core.doctor_manager import DoctorManager
manager = DoctorManager()
groups = manager.get_all_groups()
print('医生分组:', groups)
"
```

## 📊 性能测试

### 1. 简单压力测试

```bash
# 安装测试工具
pip install requests

# 创建简单压力测试脚本
cat > performance_test.py << 'EOF'
import requests
import time
import threading

def test_login():
    """测试登录功能"""
    url = 'http://localhost:5000/login'
    data = {
        'username': 'test_user',
        'password': '123456'
    }
    
    start_time = time.time()
    response = requests.post(url, data=data)
    end_time = time.time()
    
    print(f"登录测试 - 状态码: {response.status_code}, 耗时: {end_time - start_time:.2f}秒")

def test_homepage():
    """测试主页访问"""
    url = 'http://localhost:5000/'
    
    start_time = time.time()
    response = requests.get(url)
    end_time = time.time()
    
    print(f"主页测试 - 状态码: {response.status_code}, 耗时: {end_time - start_time:.2f}秒")

# 并发测试
threads = []
for i in range(10):
    thread = threading.Thread(target=test_homepage)
    threads.append(thread)
    thread.start()

for thread in threads:
    thread.join()

print("并发测试完成")
EOF

# 运行性能测试
python performance_test.py
```

### 2. 内存使用监控

```bash
# 安装监控工具
pip install psutil

# 创建内存监控脚本
cat > memory_monitor.py << 'EOF'
import psutil
import time
import os

def monitor_memory():
    """监控应用内存使用"""
    process = psutil.Process(os.getpid())
    
    while True:
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        print(f"内存使用: {memory_mb:.2f} MB")
        time.sleep(5)

if __name__ == "__main__":
    monitor_memory()
EOF
```

## 📋 测试报告模板

### 测试记录表

| 测试项目 | 测试结果 | 备注 |
|---------|---------|------|
| 环境安装 | ✅/❌ | |
| 数据库初始化 | ✅/❌ | |
| 用户注册 | ✅/❌ | |
| 用户登录 | ✅/❌ | |
| 文件上传 | ✅/❌ | |
| 绩效计算 | ✅/❌ | |
| 报告下载 | ✅/❌ | |
| 医生管理 | ✅/❌ | |
| 错误处理 | ✅/❌ | |

### 性能测试记录

| 指标 | 数值 | 标准 | 结果 |
|------|------|------|------|
| 页面加载时间 | ___ 秒 | < 3秒 | ✅/❌ |
| 登录响应时间 | ___ 秒 | < 2秒 | ✅/❌ |
| 文件上传时间 | ___ 秒 | < 30秒 | ✅/❌ |
| 计算处理时间 | ___ 秒 | < 60秒 | ✅/❌ |
| 并发用户数 | ___ 个 | > 10个 | ✅/❌ |

## 🚀 测试通过后的下一步

当所有本地测试通过后，您可以：

1. **代码提交**: 将代码提交到Git仓库
2. **准备云端环境**: 按照《腾讯云部署指南.md》准备云服务
3. **数据迁移**: 将测试数据和配置迁移到云端
4. **生产部署**: 在腾讯云上部署应用
5. **用户培训**: 组织医护人员进行系统培训

## 📞 获取帮助

如果在测试过程中遇到问题：

1. 检查控制台输出的错误信息
2. 查看日志文件 `logs/` 目录
3. 确认所有依赖都已正确安装
4. 检查文件权限和路径设置
5. 参考常见问题解决方案

测试成功后，您就可以信心满满地部署到腾讯云了！🎉